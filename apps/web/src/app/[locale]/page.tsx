import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getTranslations } from 'next-intl/server'
import { getStoreConfig, TBasePageProps, validateHttpStatus } from '@ninebot/core'

import { HomePage } from '@/businessComponents'
import { TLocales } from '@/i18n/type'

type THomePageProps = TBasePageProps<void, void, TLocales>

/**
 * 生成静态参数 - 使动态路由支持ISR
 */
export async function generateStaticParams() {
  // 返回所有支持的locale
  return [
    { locale: 'zh-Hans' }
    // 如果启用其他语言，在这里添加
    // { locale: 'en-US' }
  ]
}

/**
 * 生成元数据
 */
export async function generateMetadata({ params }: THomePageProps): Promise<Metadata> {
  const { locale } = params
  const t = await getTranslations({ locale, namespace: 'Metadata' })

  // 使用相对路径，避免动态headers()调用导致ISR失效
  const canonicalPath = '/'

  try {
    const response = await getStoreConfig()

    // 检查请求是否成功
    if (!validateHttpStatus(response.status)) {
      return notFound()
    }

    const seoConfig = response.data?.storeConfig

    if (seoConfig) {
      const title = seoConfig?.default_title || t('home_page')
      const keywords = seoConfig?.default_keywords || t('home_page')
      const description = seoConfig?.default_description || t('home_page')

      return {
        title: {
          absolute: title,
        },
        keywords: keywords,
        description: description,
        openGraph: {
          title,
          description: keywords,
          images: [],
          locale: locale,
          type: 'website',
        },
        alternates: {
          canonical: canonicalPath,
        },
      }
    }

    return {
      title: {
        absolute: t('home_page'),
      },
      keywords: t('home_page'),
      description: t('home_page'),
      openGraph: {
        title: t('home_page'),
        description: t('home_page'),
        images: [],
        locale: locale,
        type: 'website',
      },
      alternates: {
        canonical: canonicalPath,
      },
    }
  } catch {
    return {
      title: {
        absolute: t('home_page'),
      },
      keywords: t('home_page'),
      description: t('home_page'),
      openGraph: {
        title: t('home_page'),
        description: t('home_page'),
        images: [],
        locale: locale,
        type: 'website',
      },
      alternates: {
        canonical: canonicalPath,
      },
    }
  }
}

/**
 * 首页
 */
const Page = async () => {
  return <HomePage />
}

export const revalidate = 90

export default Page
