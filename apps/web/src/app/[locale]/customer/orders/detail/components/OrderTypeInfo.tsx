import { Fragment } from 'react'
import { useTranslations } from 'next-intl'
import type {
  Membership,
  OrderCoupon,
  OrderDetail,
  OrderDetailItem,
  OrderVouchers,
} from '@ninebot/core'
import { Button } from 'antd'

import { CustomImage } from '@/components'
import { IconArrow } from '@/components/icons'
import { useRouter } from '@/i18n/navigation'

import { CouponPopup, InstructionPopup, MembershipPopup } from '../components/popup'
import { useOrderDetail } from '../context/orderDetailContext'

import AddressBar from './AddressBar'
import { House } from './icons'
import OrderPackageInfo from './OrderPackageInfo'
import ProductItem from './ProductItem'

type RefundFailureInfoProps = {
  requisition: { reason?: string | null } | null | undefined
  generateOSSUrl: (url: string) => string
  getI18nString: (key: string, params?: Record<string, string>) => string
}

type CarInfoProps = {
  car_vin:
    | Array<{ device_image?: string; device_name?: string; snNo?: string } | null>
    | null
    | undefined
  getI18nString: (key: string, params?: Record<string, string>) => string
}

type VoucherInfoType =
  | {
      item_count?: number | string | null
      expired_at?: string | null
      status?: string | null
      status_label?: string | null
      items?: Membership[] | OrderCoupon | OrderVouchers | null
      waiting_use_count?: number | null
      __typename?: string
    }
  | undefined
  | null

type RedeemableVoucherItemProps = {
  isSkinSound?: boolean
  item: OrderDetailItem
  voucherInfo?: VoucherInfoType
  onButtonClick?: () => void
  getI18nString: (key: string, params?: Record<string, string>) => string
  handleOpenInstruction?: (html: string) => void
}

type PickupInfoProps = {
  orderData: OrderDetail
  setPickupVisible: (visible: boolean) => void
  getI18nString: (key: string, params?: Record<string, string>) => string
  item: OrderDetailItem
}

/**
 * 退款失败信息组件
 */
const RefundFailureInfo = ({
  requisition,
  generateOSSUrl,
  getI18nString,
}: RefundFailureInfoProps) => {
  if (!requisition) return null

  return (
    <div className="flex items-center rounded-[8px] bg-[#FF7B211A] px-[16px] py-[12px]">
      <CustomImage
        width={18}
        height={18}
        src={generateOSSUrl('/icons/close-red.png')}
        alt="close"
      />
      <div className="ml-base-12">
        <div className="text-[14px] font-medium">{getI18nString('review_rejected')}</div>
        {requisition?.reason ? (
          <div className="mt-[4px] font-miSansRegular330 text-[12px] leading-[18px] text-[#86868B]">
            {requisition?.reason}
          </div>
        ) : null}
      </div>
    </div>
  )
}

/**
 * 车辆信息组件
 */
const CarInfo = ({ car_vin, getI18nString }: CarInfoProps) => {
  if (!car_vin || car_vin.length === 0) return null

  return (
    <div className="border-t border-gray-base pt-base-48">
      <h2 className="mb-base-32 font-miSansDemiBold450 text-[28px] leading-[1.2]">
        {getI18nString('car_info')}
      </h2>
      <div className="space-y-base-16 text-lg">
        {car_vin.map((car, index) => (
          <div key={index} className="rounded-[12px] bg-white p-base-16">
            <CustomImage
              className="mb-base-12 rounded-[8px] bg-[#F8F8F9] object-cover"
              width={88}
              height={88}
              src={car?.device_image || ''}
              alt={car?.device_name || ''}
            />
            <div className="mb-base-8 gap-base-8 flex items-center text-base">
              <span className="text-gray-3">{getI18nString('car_modal')}：</span>
              <span>{car?.device_name}</span>
            </div>
            <div className="mb-base-8 gap-base-8 flex items-center text-base">
              <span className="text-gray-3">{getI18nString('sn')}：</span>
              <span>{car?.snNo}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

/**
 * 可兑换券通用组件（包含代金券、服务包、数字会员）
 */
const RedeemableVoucherItem = ({
  isSkinSound,
  item,
  voucherInfo,
  onButtonClick,
  getI18nString,
  handleOpenInstruction,
}: RedeemableVoucherItemProps) => {
  if (!isSkinSound && (!voucherInfo || Number(voucherInfo?.item_count || 0) <= 0)) {
    return null
  }

  return (
    <div className="flex flex-col gap-base-24 rounded-[16px] border border-[#E1E1E4] p-base-24">
      <div className="border-b border-gray-base pb-base-24 text-2xl">{item?.product?.name}</div>
      <div className="flex items-center justify-between">
        <ProductItem
          productInfo={item}
          isShowInstruction={isSkinSound ? false : !!item?.product?.description?.html}
          setInstructionVisible={() =>
            handleOpenInstruction && handleOpenInstruction(item?.product?.description?.html || '')
          }
        />
        {!isSkinSound ? (
          <div className="flex flex-col items-center gap-[4px]">
            <div className="text-[16px] leading-[1.4]">
              <span>消费券码 </span>
              <span className="font-miSansDemiBold450 text-[20px] text-primary">
                {voucherInfo?.item_count}
              </span>
              <span> 张</span>
            </div>
            <span className="font-miSansRegular330 text-[12px] leading-[16px] text-[#86868B]">
              {getI18nString('expired_time_until')} {voucherInfo?.expired_at?.split(' ')[0]}
            </span>
          </div>
        ) : (
          <div className="flex flex-col items-center gap-[4px]">
            <span className="font-miSansRegular330 text-[12px] leading-[16px] text-[#86868B]">
              {getI18nString('valid_time', {
                key: item?.use_from_date || '',
                key2: item?.use_to_date || '',
              })}
            </span>
          </div>
        )}
        {isSkinSound ? (
          <Button
            type={
              item?.requisition_status_label || item?.digital_info?.status === 0
                ? 'primary'
                : 'default'
            }>
            <div className="flex items-center gap-[4px]">
              {item?.requisition_status_label || item?.digital_info?.status === 0 ? (
                <span className="text-lg text-white">
                  {item?.requisition_status_label || item?.digital_info?.label}
                </span>
              ) : (
                <span>{item?.digital_info?.label}</span>
              )}
            </div>
          </Button>
        ) : (
          <Button
            type={voucherInfo?.status === '0' ? 'primary' : 'default'}
            onClick={onButtonClick}>
            <div className="flex items-center gap-[4px]">
              {voucherInfo?.status === '0' ? (
                <span className="text-lg text-white">{getI18nString('view_coupon_code')}</span>
              ) : (
                <span>{voucherInfo?.status_label}</span>
              )}

              <IconArrow color={voucherInfo?.status === '0' ? '#fff' : '#000'} rotate={-90} />
            </div>
          </Button>
        )}
      </div>
    </div>
  )
}

/**
 * 自提信息组件
 */
const PickupInfo = ({ orderData, setPickupVisible, getI18nString, item }: PickupInfoProps) => {
  if (!orderData?.pickup_phone || !item?.store_info) return null

  return (
    <div className="flex flex-col gap-base-24 rounded-[16px] border border-[#E1E1E4] p-base-24">
      <div className="flex items-center gap-base text-[20px] leading-[1.4] text-[#0F0F0F]">
        <House big />
        {orderData?.store_info?.[0]?.store_name || '门店'}
      </div>

      <AddressBar
        address={orderData?.store_info?.[0]?.store_address || ''}
        phone={orderData?.store_info?.[0]?.telephone || ''}
      />
      <div className="flex items-center justify-between gap-[12px] 2xl:gap-[16px]">
        <ProductItem productInfo={item} showAttr={false} />

        {!!orderData?.pickup_phone && (
          <div className="flex flex-col items-center gap-2">
            <span className="text-lg">{orderData?.pickup_phone}</span>
            <span className="text-sm text-gray-3">{getI18nString('pickup_phone')}</span>
          </div>
        )}

        {!!orderData?.remark_comment && (
          <div className="flex min-w-0 max-w-[180px] flex-shrink-0 flex-col items-center gap-2">
            <span
              className="line-clamp-5 overflow-hidden break-words text-lg"
              title={orderData?.remark_comment}
              style={{ wordBreak: 'break-word', overflowWrap: 'break-word' }}>
              {orderData?.remark_comment}
            </span>
            <span className="text-sm text-gray-3">{getI18nString('remark')}</span>
          </div>
        )}

        {!!orderData?.pickup_info?.status_label && (
          <Button
            type="primary"
            onClick={() => {
              if (orderData?.can_cancel_requisition === true) {
                return
              }
              setPickupVisible(true)
            }}
            disabled={orderData?.can_cancel_requisition === true}>
            <div className="flex flex-row items-center gap-2">
              <span className="text-lg text-white">{orderData?.pickup_info?.status_label}</span>
              <IconArrow color="#fff" rotate={-90} />
            </div>
          </Button>
        )}
      </div>
    </div>
  )
}

/**
 * 订单类型信息组件
 * 负责展示订单详情中的各类信息：退款信息、车辆信息、代金券、服务包、数字会员和自提信息等
 */
export default function OrderTypeInfo() {
  const {
    orderData,
    generateOSSUrl,
    setPickupVisible,
    setCouponList,
    setCouponVisible,
    setMembershipList,
    setMembershipVisible,
    setMembershipProductInfo,
    isTracking,
    instructionVisible,
    setInstructionVisible,
    instructionData,
    couponList,
    couponVisible,
    copyToClipboard,
    pickupVisible,
    membershipList,
    membershipVisible,
    membershipProductInfo,
    handleOpenInstruction,
    isSkinSound,
  } = useOrderDetail()
  const getI18nString = useTranslations('Common')
  const router = useRouter()

  return (
    <>
      {/* 主内容区域 */}
      <div className="mt-base-48 space-y-base-48">
        {/* 退款失败原因 */}
        <RefundFailureInfo
          requisition={orderData?.requisition}
          generateOSSUrl={generateOSSUrl}
          getI18nString={getI18nString}
        />

        {/* 车辆信息 */}
        <CarInfo car_vin={orderData?.car_vin} getI18nString={getI18nString} />

        {/* 商品项信息区 */}
        {orderData?.items?.map((item) => (
          <Fragment key={item?.id}>
            {/* 代金券信息 */}
            <RedeemableVoucherItem
              item={item}
              voucherInfo={item?.coupons}
              getI18nString={getI18nString}
              handleOpenInstruction={handleOpenInstruction}
              onButtonClick={() => router.push('/customer/coupons')}
            />

            {/* 服务包信息 */}
            <RedeemableVoucherItem
              item={item}
              voucherInfo={item?.vouchers}
              getI18nString={getI18nString}
              handleOpenInstruction={handleOpenInstruction}
              onButtonClick={() => {
                if (item?.vouchers?.items) {
                  setCouponList(item?.vouchers?.items)
                  setCouponVisible(true)
                }
              }}
            />

            {/* 数字会员信息 */}
            <RedeemableVoucherItem
              item={item}
              voucherInfo={item?.third_platform_member_code}
              getI18nString={getI18nString}
              handleOpenInstruction={handleOpenInstruction}
              onButtonClick={() => {
                if (item?.third_platform_member_code?.items) {
                  setMembershipList(item?.third_platform_member_code?.items)
                  setMembershipProductInfo(item)
                  setMembershipVisible(true)
                }
              }}
            />

            {/* 皮肤音效信息 */}
            <RedeemableVoucherItem
              isSkinSound={isSkinSound}
              item={item}
              getI18nString={getI18nString}
            />

            {/* 自提信息 */}
            <PickupInfo
              orderData={orderData}
              setPickupVisible={setPickupVisible}
              getI18nString={getI18nString}
              item={item}
            />
          </Fragment>
        ))}

        {/* 包裹信息 */}
        {isTracking && <OrderPackageInfo />}
      </div>

      {/* 弹窗组件区域 */}
      {/* 使用说明弹窗 */}
      <InstructionPopup
        popupVisible={instructionVisible}
        closePopup={() => {
          setInstructionVisible(false)
        }}
        data={instructionData}
      />

      {/* 取货码弹窗 */}
      {orderData?.pickup_info && (
        <CouponPopup
          pickupInfo={orderData?.pickup_info}
          copyToClipboard={copyToClipboard}
          popupVisible={pickupVisible}
          closePopup={() => {
            setPickupVisible(false)
          }}
          productInfo={
            orderData?.items?.[0]?.product
              ? {
                  name: orderData.items[0].product.name || '',
                  image: orderData.items[0].product.image?.url || '',
                }
              : undefined
          }
        />
      )}

      {/* 卷码弹窗 */}
      <CouponPopup
        couponList={couponList}
        copyToClipboard={copyToClipboard}
        popupVisible={couponVisible}
        closePopup={() => {
          setCouponVisible(false)
        }}
      />

      {/* 数字会员弹窗 */}
      <MembershipPopup
        data={membershipList}
        productInfo={membershipProductInfo}
        copyToClipboard={copyToClipboard}
        popupVisible={membershipVisible}
        closePopup={() => {
          setMembershipVisible(false)
        }}
      />
    </>
  )
}
