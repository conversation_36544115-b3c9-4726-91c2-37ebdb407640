import { IconPlus, NCoinView, Price } from '@ninebot/core'

import { useOrderDetail } from '../../context/orderDetailContext'

/**
 * 订单结算组件
 * 显示订单的总价、折扣、N币抵扣、运费和最终支付金额
 */
export default function OrderSettlement() {
  const {
    isMigrated, // 是否迁移的订单
    orderData, // 订单数据
    getI18nString, // 国际化函数
    isUnPaid, // 是否未支付
    hasNCoin, // 是否有N币支付
    isOnlyNCoin, // 是否仅N币支付
    migrationTotalNCoin, // 迁移订单的总N币
    isAllVirtualProduct, // 是否全部为虚拟产品
    hasBothDiscounts, // 是否同时存在两种折扣
  } = useOrderDetail()

  /**
   * 渲染总价部分
   * 根据支付方式不同（仅N币、混合支付、普通支付）显示不同内容
   */
  const renderTotalPrice = () => {
    if (isOnlyNCoin) {
      return (
        <NCoinView
          number={isMigrated ? (migrationTotalNCoin ?? 0) : (orderData?.ncoin_pay?.subtotal ?? 0)}
          iconStyle={{ size: 16 }}
          textStyle="text-lg "
          showZero
        />
      )
    }

    if (hasNCoin) {
      return (
        <div className="flex items-center">
          <Price
            price={orderData?.total?.subtotal}
            currencyStyle="text-lg"
            textStyle="text-lg"
            fractionStyle="text-lg"
          />
          <div className="mx-[4px]">
            <IconPlus />
          </div>
          <NCoinView
            number={orderData?.ncoin_pay?.subtotal ?? 0}
            iconStyle={{ size: 16 }}
            textStyle="text-lg"
            showZero
          />
        </div>
      )
    }

    return (
      <Price
        price={orderData?.total?.subtotal}
        currencyStyle="text-lg"
        textStyle="text-lg"
        fractionStyle="text-lg"
      />
    )
  }

  /**
   * 渲染折扣部分
   * 处理同时存在两种折扣和单一折扣的情况
   */
  const renderDiscounts = () => {
    const discountLabel = orderData?.total?.discounts?.[0]?.label
      ? `(${orderData?.total?.discounts?.[0]?.label})`
      : ''

    if (hasBothDiscounts) {
      return (
        <>
          <div className="flex items-center justify-between py-1">
            <span>
              {getI18nString('discount_price')}
              {discountLabel}
            </span>
            <div className="flex items-center justify-end">
              <span className="mr-2 text-primary">-</span>
              <Price
                price={{
                  ...orderData?.total?.discounts?.[0]?.amount,
                  value: orderData?.total?.discounts?.[0]?.distribute_amount,
                }}
                currencyStyle="text-lg text-primary"
                textStyle="text-lg text-primary"
                fractionStyle="text-lg text-primary"
              />
            </div>
          </div>
          <div className="flex items-center justify-between py-1">
            <div className="flex items-center justify-end">
              <span className="text-primary">-</span>
              <NCoinView
                iconStyle={{ size: 14, background: '#DA291C' }}
                textStyle="text-lg text-primary"
                number={orderData?.total?.discounts?.[0]?.distribute_ncoin ?? 0}
              />
            </div>
          </div>
        </>
      )
    }

    return (
      <div className="flex items-center justify-between py-1">
        <span>
          {getI18nString('discount_price')}
          <span dangerouslySetInnerHTML={{ __html: discountLabel }}></span>
        </span>
        <div className="flex items-center justify-end">
          <span className="mr-2 text-primary">-</span>
          {Number(orderData?.total?.discounts?.[0]?.distribute_ncoin) > 0 ? (
            <NCoinView
              iconStyle={{ size: 14, background: '#DA291C' }}
              textStyle="text-lg text-primary"
              number={orderData?.total?.discounts?.[0]?.distribute_ncoin ?? 0}
            />
          ) : (
            <Price
              price={orderData?.total?.discounts?.[0]?.amount}
              currencyStyle="text-lg text-primary"
              textStyle="text-lg text-primary"
              fractionStyle="text-lg text-primary"
            />
          )}
        </div>
      </div>
    )
  }

  /**
   * 渲染N币抵扣部分
   * 当非仅N币支付且有N币抵扣时显示
   */
  const renderNCoinDeduction = () => {
    if (!isOnlyNCoin && orderData?.ncoin_amount) {
      return (
        <div className="flex items-center justify-between">
          <span>{getI18nString('n-coin_title')}</span>
          <div className="flex items-center">
            <span className="mr-2 text-primary">-</span>
            <Price
              price={{ value: orderData?.ncoin_amount }}
              currencyStyle="text-lg text-primary"
              textStyle="text-lg text-primary"
              fractionStyle="text-lg text-primary"
            />
          </div>
        </div>
      )
    }
    return null
  }

  /**
   * 渲染运费部分
   * 当非全部为虚拟产品时显示
   */
  const renderShipping = () => {
    if (!isAllVirtualProduct) {
      return (
        <div className="mb-0 flex items-center justify-between">
          <span>{getI18nString('shipping_price')}</span>
          {orderData?.total?.total_shipping?.value ? (
            <Price
              price={orderData?.total?.total_shipping}
              currencyStyle="text-lg"
              textStyle="text-lg"
              fractionStyle="text-lg"
            />
          ) : (
            <span>{getI18nString('shipping_free')}</span>
          )}
        </div>
      )
    }
    return null
  }

  /**
   * 渲染最终支付金额
   * 根据支付方式不同显示不同内容
   */
  const renderFinalPayment = () => {
    if (isOnlyNCoin) {
      return (
        <NCoinView
          number={
            isMigrated ? (migrationTotalNCoin ?? 0) : (orderData?.ncoin_pay?.grand_total ?? 0)
          }
          iconStyle={{ size: 24 }}
          textStyle="text-3xl leading-[1.2]"
          showZero
        />
      )
    }

    if (hasNCoin) {
      return (
        <>
          <Price
            price={orderData?.total?.grand_total}
            currencyStyle="text-3xl leading-[1.2] text-primary"
            textStyle="text-3xl leading-[1.2] text-primary"
            fractionStyle="text-3xl leading-[1.2] text-primary"
          />
          <div className="mx-[4px]">
            <IconPlus />
          </div>
          <NCoinView
            number={orderData?.ncoin_pay?.grand_total ?? 0}
            iconStyle={{ size: 24 }}
            textStyle="text-3xl leading-[1.2]"
            showZero
          />
        </>
      )
    }

    return (
      <Price
        price={orderData?.total?.grand_total}
        currencyStyle="text-3xl leading-[1.2] text-primary"
        textStyle="text-3xl leading-[1.2] text-primary"
        fractionStyle="text-3xl leading-[1.2] text-primary"
      />
    )
  }

  return (
    <div className="mt-base-48 flex justify-end font-miSansRegular330">
      <div className="w-[412px]">
        {/* 价格明细区域 */}
        <div className="flex flex-col gap-base-32 text-lg">
          {/* 总价部分 */}
          <div className="flex items-center justify-between">
            <span>{getI18nString('total_price')}</span>
            {renderTotalPrice()}
          </div>

          {/* 折扣部分 */}
          {renderDiscounts()}

          {/* N币抵扣部分 */}
          {renderNCoinDeduction()}

          {/* 运费部分 */}
          {renderShipping()}
        </div>

        {/* 最终支付金额 */}
        <div className="mt-base-24 flex items-center justify-between border-t border-gray-base pt-base-24">
          <div className="text-[16px] leading-[1.4] text-[#000000A6]">
            {isUnPaid ? getI18nString('should_pay') : getI18nString('has_paid')}
          </div>
          <div className="flex items-center justify-end">{renderFinalPayment()}</div>
        </div>
      </div>
    </div>
  )
}
