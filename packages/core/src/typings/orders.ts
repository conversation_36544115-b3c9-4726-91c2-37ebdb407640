import {
  CurrencyEnum,
  GetAfterSaleDetailQuery,
  GetAfterSalesQuery,
  GetAfterSaleTrackQuery,
  GetOrderDetailQuery,
  GetOrderLogisticsQuery,
  GetOrderRequisitionListQuery,
  GetOrdersQuery,
} from '../graphql/generated/graphql'

export type OrderData = NonNullable<
  NonNullable<NonNullable<NonNullable<GetOrdersQuery>['customer']>['orders']>['items']
>

export type OrderItem = NonNullable<
  NonNullable<NonNullable<NonNullable<GetOrdersQuery>['customer']>['orders']>['items']
>[number]

export type AfterSalesOrderData = NonNullable<
  NonNullable<NonNullable<GetAfterSalesQuery>['requisitionList']>['items']
>

export type AfterSalesOrderItem = NonNullable<
  NonNullable<NonNullable<NonNullable<GetAfterSalesQuery>['requisitionList']>['items']>[number]
>

export type OrderListItem = OrderItem & AfterSalesOrderItem

export type OrderTracking = NonNullable<
  NonNullable<GetOrderLogisticsQuery>['getOrderTracking']
>[number]

export type OrderReturnTracking = NonNullable<GetAfterSaleTrackQuery>['requisitionTrackDetail']

export type OrderReturnTrackingItem = NonNullable<
  NonNullable<OrderReturnTracking>['tracks']
>[number]

export type OrderReturnProducts = NonNullable<
  NonNullable<GetOrderRequisitionListQuery>['orderRequisitionList']
>['items']

export type OrderReturnProduct = NonNullable<OrderReturnProducts>[number]

export type OrderReturnDetail = NonNullable<
  NonNullable<NonNullable<GetAfterSaleDetailQuery>['requisitionList']>['items']
>[number]

export type OrderReturnDetailItem = NonNullable<NonNullable<OrderReturnDetail>['items']>[number]

export type OrderReturnItem = OrderReturnProduct & {
  check: boolean
  ordered: number
  item_id: string
}

export type OrderReturnFormatItem = {
  item_id: string
  qty: number
}

export type OrderReturnCarrier = NonNullable<
  NonNullable<OrderReturnDetail>['return_carriers']
>[number]

export type OrderDisabledReturnProducts = NonNullable<
  NonNullable<GetOrderRequisitionListQuery>['orderRequisitionList']
>['requested_items']

export type OrderDetail = NonNullable<
  NonNullable<
    NonNullable<NonNullable<NonNullable<GetOrderDetailQuery>['customer']>['orders']>['items']
  >[number]
>

export type OrderDetailItem = NonNullable<NonNullable<OrderDetail>['items']>[number]

export type OrderMigratedDetail = NonNullable<OrderDetail>['migration_data']

export type OrderMigratedDetailItem = NonNullable<NonNullable<OrderMigratedDetail>['items']>[number]

export type OrderPickupStore = NonNullable<
  NonNullable<NonNullable<OrderDetail>['store_info']>[number]
>

export type OrderShipmentTracking = NonNullable<
  NonNullable<OrderDetail>['shipment_tracking']
>[number]

export type OrderPickupInfo = NonNullable<NonNullable<OrderDetail>['pickup_info']>

export type PriceRanges = {
  maximum_price: MaximumPrice
}

type MaximumPrice = {
  discount: Discount
  final_price: OrderPrice
  regular_price: OrderPrice
}

type Discount = {
  amount_off: number
}

export type OrderPrice = {
  currency?: CurrencyEnum | null
  value?: number | null
}

export type OrderCoupon = NonNullable<NonNullable<OrderDetailItem>['coupons']>

export type OrderVouchers = NonNullable<NonNullable<OrderDetailItem>['vouchers']>['items']

export type Membership = NonNullable<
  NonNullable<NonNullable<OrderDetailItem>['third_platform_member_code']>['items']
>[number]

export type PaymentMethod = {
  logo?: string | null | undefined
  name?: string | null | undefined
  title?: string | null | undefined
  code?: string | null | undefined
  type?: string | null | undefined
}
