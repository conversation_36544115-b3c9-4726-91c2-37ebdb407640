/* -------------------------------- 网站通用 Api -------------------------------- */
import {
  GET_CATEGORIES,
  GET_CMS_PAGE,
  GET_HOME_PAGE_CONFIG,
  GET_PRODUCTS,
  GET_STORE_CONFIG,
  RESOLVE_URL,
} from '../../graphql'
import {
  GetCategoriesQuery,
  GetCategoriesQueryVariables,
  GetCmsPageQuery,
  GetCmsPageQueryVariables,
  GetHomePageConfigQuery,
  GetHomePageConfigQueryVariables,
  GetProductsQuery,
  GetProductsQueryVariables,
  GetStoreConfigQuery,
  GetStoreConfigQueryVariables,
  ResolveUrlQuery,
  ResolveUrlQueryVariables,
} from '../../graphql/generated/graphql'
import { gqlRequest } from '../../utils'

/**
 * 解析网站 URL 信息
 */
export const resolveUrlQuery = async (
  params: ResolveUrlQueryVariables,
  options: RequestInit = {},
) => {
  const response = await gqlRequest.query<ResolveUrlQuery, ResolveUrlQueryVariables>(
    RESOLVE_URL,
    params,
    {
      next: {
        revalidate: 300,
        tags: [`resolveUrl-${params.url}`],
      },
      ...options,
    },
  )

  return response
}

/**
 * 获取分类数据
 */
export const getCategoriesQuery = async (
  params: GetCategoriesQueryVariables,
  options: RequestInit = {},
) => {
  const response = await gqlRequest.query<GetCategoriesQuery, GetCategoriesQueryVariables>(
    GET_CATEGORIES,
    params,
    {
      next: { revalidate: 300 },
      ...options,
    },
  )

  return response
}

/**
 * 获取商品数据
 */
export const getProductsQuery = async (
  params: GetProductsQueryVariables,
  options: RequestInit = {},
) => {
  const response = await gqlRequest.query<GetProductsQuery, GetProductsQueryVariables>(
    GET_PRODUCTS,
    params,
    {
      next: { revalidate: 300 },
      ...options,
    },
  )

  return response
}

/**
 * 获取 cms 页面数据
 */
export const getCmsPageQuery = async (
  params: GetCmsPageQueryVariables,
  options: RequestInit = {},
) => {
  const response = await gqlRequest.query<GetCmsPageQuery, GetCmsPageQueryVariables>(
    GET_CMS_PAGE,
    params,
    {
      next: { revalidate: 300 },
      ...options,
    },
  )

  return response
}

/**
 * 获取首页配置
 */
export const getHomePageConfig = async (
  params: GetHomePageConfigQueryVariables,
  options: RequestInit = {},
) => {
  const response = await gqlRequest.query<GetHomePageConfigQuery, GetHomePageConfigQueryVariables>(
    GET_HOME_PAGE_CONFIG,
    params,
    {
      next: { revalidate: 300 },
      ...options,
    },
  )

  return response
}

/**
 * 获取站点配置信息
 */
export const getStoreConfig = async (options: RequestInit = {}) => {
  const response = await gqlRequest.query<GetStoreConfigQuery, GetStoreConfigQueryVariables>(
    GET_STORE_CONFIG,
    {},
    {
      next: { revalidate: 300 },
      ...options,
    },
  )

  return response
}
