'use client'

import { useEffect } from 'react'

import { QUID_KEY } from '../../constants'
import { useGetStoreConfigQuery } from '../../services'
import { gustIdSelector, quidSelector, setGustId, setQUID, setStoreConfig } from '../../store'
import { useAppDispatch, useAppSelector } from '../../store/hooks'
import { TBaseComponentProps } from '../../typings'
import { appCookie } from '../../utils'
import { defaultCookieOptions } from '../../utils/cookie'
import { generateEncryptedQUID, generateGustId } from '../../utils/helper'
type TGlobalProvider = TBaseComponentProps & {
  isPurePage?: boolean
  platform?: 'web' | 'h5'
}

/**
 * 全局 Provider
 */
const GlobalProvider = ({ children, isPurePage = false, platform = 'web' }: TGlobalProvider) => {
  const dispatch = useAppDispatch()

  const { data: storeConfigData } = useGetStoreConfigQuery(
    {},
    {
      skip: isPurePage,
    },
  )
  const gustId = useAppSelector(gustIdSelector)
  const quid = useAppSelector(quidSelector)

  /**
   * 控制 loading
   */
  // useEffect(() => {
  //   if (storeConfigLoading) {
  //     loading.show()
  //   } else {
  //     loading.hide()
  //   }
  // }, [storeConfigLoading, loading])

  /**
   * 保存 storeConfig 数据
   */
  useEffect(() => {
    if (storeConfigData?.storeConfig) {
      dispatch(setStoreConfig(storeConfigData.storeConfig))
    }
  }, [dispatch, storeConfigData])

  /**
   * 保存 gustId 数据
   */
  useEffect(() => {
    if (!gustId) {
      const NewGustId = generateGustId()
      dispatch(setGustId(NewGustId))
    }
  }, [dispatch, gustId])

  /**
   * 客户端初始化 QUID（如果 StoreProvider 中没有设置）
   */
  useEffect(() => {
    if (!quid) {
      // 先尝试从 cookie 获取
      let currentQuid = appCookie.getItemSync<string>(QUID_KEY)

      // 如果 cookie 中没有，则生成新的
      if (!currentQuid) {
        const generatedQuid = generateEncryptedQUID(platform)
        currentQuid = (generatedQuid as string) || null
      }

      if (currentQuid) {
        dispatch(setQUID(currentQuid))
        // 将 quid 保存到 cookies 中
        appCookie.setItem(QUID_KEY, currentQuid, {
          ...defaultCookieOptions,
          expires: new Date(Date.now() + 1000 * 60 * 60 * 24), // 1 天
        })
      }
    }
  }, [dispatch, quid, platform])

  // useEffect(() => {
  //   const loadVConsole = async () => {
  //     const VConsole = (await import('vconsole')).default
  //     try {
  //       new VConsole()
  //     } catch (err) {
  //       console.log(err)
  //     }
  //   }

  //   loadVConsole()
  // }, [])

  return <>{children}</>
}

export default GlobalProvider
