# CODEBUDDY.md

This file provides guidance to CodeBuddy Code when working with this Ninebot web application repository.

## Development Commands

**Root level commands (run from repository root):**
```bash
pnpm dev           # Start all apps in development (web on 3000, h5 on 3001)
pnpm build         # Build all apps
pnpm lint          # Lint all apps with ESLint
pnpm type-check    # TypeScript type checking for all apps
pnpm format        # Format code with Prettier
pnpm clean         # Clean build outputs
pnpm clean:cache   # Clean Turbo cache
```

**Individual app commands (run from apps/web/ or apps/h5/):**
```bash
pnpm dev           # Start single app
pnpm build         # Build single app
pnpm lint          # Lint single app
pnpm type-check    # Type check single app
pnpm codegen:dev   # Generate GraphQL types from dev environment
pnpm codegen:prod  # Generate GraphQL types from prod environment
```

**Production deployment:**
```bash
pnpm deploy:master # Deploy as master instance with PM2
pnpm deploy:slave  # Deploy as slave instance with PM2
```

## Project Architecture

This is a **Turborepo monorepo** with the following structure:

- **Web app** (`apps/web/`) - Desktop application using Ant Design (port 3000)
- **H5 app** (`apps/h5/`) - Mobile application using Ant Design Mobile (port 3001)
- **Shared packages:**
  - `packages/core/` - Shared Redux store, utilities, and business logic
  - `packages/eslint-config/` - Shared ESLint configuration
  - `packages/tailwind-config/` - Shared Tailwind configuration

**Tech Stack:**
- Next.js 14 with App Router
- TypeScript 5.5.4
- React 18
- Redux Toolkit (in packages/core)
- GraphQL with code generation
- Tailwind CSS + Ant Design (web) / Ant Design Mobile (h5)
- next-intl for internationalization
- Redis for caching

## Key Development Notes

**Package Management:**
- Uses pnpm workspace with `workspace:*` protocol for internal dependencies
- Node.js >= 20.16.0 required (see .nvmrc)
- pnpm >= 9.7.1 required

**GraphQL Workflow:**
- GraphQL queries require code generation before building
- Run `pnpm codegen:dev` when GraphQL schema changes
- Uses `NODE_TLS_REJECT_UNAUTHORIZED=0` for development SSL issues

**Code Quality:**
- Pre-commit hooks enforce ESLint, Prettier, and Stylelint via lint-staged
- Conventional commits enforced by commitlint
- Always run `pnpm type-check` before committing

**Application Structure:**
- Both apps share Redux store from `packages/core`
- Web app uses standard Ant Design components
- H5 app uses Ant Design Mobile with responsive pixel conversion
- Internationalization configured via next-intl in both apps

**Deployment:**
- Uses PM2 for process management
- Separate master/slave deployment configurations
- Environment-specific GraphQL codegen required before production builds