# Ninebot Web 项目渲染模式分析报告

## 概述

本项目是基于 Next.js 14 App Router 的 Turborepo 单体仓库，采用 SSR + CSR 混合渲染模式，支持桌面端和移动端双应用架构。

## 主要渲染模式

### SSR (Server-Side Rendering) + CSR (Client-Side Rendering)

项目采用 Next.js 14 的混合渲染策略，结合服务端渲染和客户端渲染的优势。

## 技术架构

### 应用结构
- **Web 应用** (`apps/web/`) - 桌面端应用
  - 端口：3000
  - UI 框架：Ant Design
  - 目标用户：桌面端用户

- **H5 应用** (`apps/h5/`) - 移动端应用
  - 端口：3001
  - UI 框架：Ant Design Mobile
  - 目标用户：移动端用户

### 核心技术栈
- **Next.js 14** - App Router 架构
- **React 18** - 前端框架
- **TypeScript 5.5.4** - 类型系统
- **Redux Toolkit** - 状态管理（共享包）
- **GraphQL** - 数据查询层
- **Tailwind CSS** - 样式框架
- **next-intl** - 国际化支持

## 渲染特性详解

### 1. 服务端渲染 (SSR)
```javascript
// next.config.js 配置
experimental: {
  serverActions: {
    allowedOrigins: isProduction ? [process.env.NEXT_PUBLIC_SHOP_API_URL || ''] : [],
  },
}
```

**特性：**
- 支持 Server Actions
- 首屏快速渲染
- SEO 友好
- 服务端数据预取

### 2. 客户端渲染 (CSR)
**特性：**
- React 18 客户端交互
- Redux Toolkit 状态管理
- 动态路由导航
- 组件懒加载

### 3. 混合渲染策略
App Router 支持页面级渲染策略选择：
- **SSG** (Static Site Generation) - 静态页面
- **SSR** (Server-Side Rendering) - 动态服务端渲染
- **ISR** (Incremental Static Regeneration) - 增量静态再生成

## 性能优化配置

### 1. 构建优化
```javascript
// 自定义构建 ID
generateBuildId: async () => {
  return `${timeStamp}`
}

// 静态资源版本控制
config.output.filename = `static/js/[name].[contenthash]-${buildId}.js`
config.output.chunkFilename = `static/js/[name].[contenthash]-${buildId}.js`
```

### 2. 图片优化
```javascript
images: {
  remotePatterns: remotePatternsImage,
  unoptimized: true,
}
```

### 3. 代码分割
- 自动 chunk splitting
- 按需加载组件
- 共享包优化 (`transpilePackages`)

### 4. 缓存策略
- **Redis 缓存** - 数据层缓存
- **自定义 buildId** - 静态资源缓存控制
- **CDN 支持** - 生产环境 `assetPrefix` 配置

## 国际化支持

### next-intl 配置
```javascript
const withNextIntl = createNextIntlPlugin()
module.exports = withNextIntl(nextConfig)
```

**特性：**
- 多语言支持
- 服务端 + 客户端国际化
- 路由级语言切换

## 部署架构

### 部署模式
**Node.js 服务器部署** - 需要 Node.js 运行时环境

### PM2 进程管理
```bash
# Master 实例部署
pnpm deploy:master

# Slave 实例部署  
pnpm deploy:slave
```

**特性：**
- 进程管理和监控
- 负载均衡支持
- 自动重启机制
- 集群模式部署

### 环境配置
- **开发环境** - `pnpm dev` (端口 3000/3001)
- **生产环境** - `next start` + PM2
- **GraphQL 代码生成** - 环境特定的 schema 同步

## 开发工作流

### 1. GraphQL 工作流
```bash
# 开发环境代码生成
pnpm codegen:dev

# 生产环境代码生成
pnpm codegen:prod
```

### 2. 构建流程
```bash
# 开发模式
pnpm dev

# 生产构建
pnpm build

# 类型检查
pnpm type-check
```

### 3. 代码质量
- **ESLint** - 代码规范检查
- **Prettier** - 代码格式化
- **Stylelint** - 样式规范
- **Commitlint** - 提交信息规范
- **lint-staged** - 预提交钩子

## 性能特点

### 优势
1. **首屏性能** - SSR 提供快速首屏渲染
2. **SEO 优化** - 服务端渲染利于搜索引擎
3. **用户体验** - CSR 提供流畅的交互体验
4. **代码复用** - 共享包架构减少重复代码
5. **缓存优化** - 多层缓存策略提升性能

### 适用场景
- **电商平台** - 需要 SEO 和快速加载
- **企业应用** - 需要复杂交互和状态管理
- **多端应用** - 桌面端和移动端统一技术栈
- **国际化应用** - 多语言和多地区支持

## 技术决策总结

项目采用 **SSR + CSR 混合渲染模式**，充分利用 Next.js 14 App Router 的灵活性，在保证首屏性能和 SEO 的同时，提供丰富的客户端交互体验。通过 Turborepo 单体仓库架构实现代码共享和统一管理，是一个现代化的全栈 Web 应用解决方案。